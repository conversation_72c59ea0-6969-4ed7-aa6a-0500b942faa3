import React, { useState } from 'react';
import './App.css'
import logo from './assets/images/logo.png';
import ebiLogo from './assets/images/logo (1).png';
import {
  TextField,
  Box,
  Typography,
  Button,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Select,
  MenuItem,
  InputLabel,
  Checkbox,
  FormGroup,
  Paper,
  Container,
  Divider,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  AppBar,
  Toolbar,
  ThemeProvider,
  useMediaQuery,
  useTheme
} from "@mui/material";
import { createTheme } from '@mui/material/styles';
import { ArrowBack, ArrowForward } from '@mui/icons-material';

interface FormData {
  // General Information
  companyName: string;

  // Energy Consumption
  energyConsumptionType: string;
  renewableEnergyElements: string[];
  nonRenewableEnergyElements: string[];
  windElectricity: string;
  solarElectricity: string;
  otherRenewableElectricity: string;
  gridElectricityConsumed: string;
  fuelConsumed: string;
  otherEnergyConsumed: string;
  totalEnergyConsumed: string;
  gridEnergyPercentage: string;
  renewablePercentage: string;
  numberOfEmployees: string;
  energyPerEmployee: string;

  // Water Consumption
  waterSource: string[];
  totalWaterWithdrawn: string;
  totalWaterConsumed: string;
  waterReused: string;
  highWaterStressRegions: string;
  waterWithdrawnStress: string;
  waterConsumedStress: string;

  // Emissions
  emissionSources: string[];
  dieselUnits: string;
  dieselCO2: string;
  petrolUnits: string;
  petrolCO2: string;
  keroseneUnits: string;
  keroseneCO2: string;
  electricityUnits: string;
  electricityCO2: string;

  // Waste Management
  wasteActions: string[];
  ewasteQuantity: string;
  ewasteHazardous: string;
  paperWasteQuantity: string;
  paperWasteHazardous: string;
  foodWasteQuantity: string;
  foodWasteHazardous: string;
  paperRecycledQuantity: string;
  paperRecycledPercentage: string;
  plasticRecycledQuantity: string;
  plasticRecycledPercentage: string;
  organicWasteQuantity: string;
  landfillWaste: string;
  landfillQuantity: string;

  // GHG Emissions
  naturalGas: string;
  distillateFuelOil: string;
  gasoline: string;
  refrigerants: string;
  purchasedElectricity: string;
  heatingCooling: string;
  travel: string;
  purchasedGoods: string;
  upstreamTransportation: string;
  wasteFromOperations: string;

  // Compliance
  ehsPractices: string;
  nonCompliance: string;
  finesPenalties: string;
  nonComplianceDetails: string;
  codeOfConduct: string;
  environmentalRegulations: string;

  // Assessments
  supplierRiskAssessment: string;
  supplierAssessmentFrequency: string;
  supplierAudits: string;
  correctiveActionPlans: string;
  carbonReductionTargets: string;

  // Financial Contributions
  financialContributions: string;
  directFinancialValue: string;
  indirectFinancialValue: string;
  inKindContributions: string;
  directInKindValue: string;
  indirectInKindValue: string;
  inKindEstimation: string;
  sbtiTargets: string;
  sbtiDescription: string;
}

const sections = [
  'General Information',
  'Energy Consumption',
  'Water Consumption',
  'Emissions',
  'GHG Emissions',
  'Waste Management',
  'Compliance',
  'Assessments',
  'Financial Contributions'
];

// Create theme with Poppins font and responsive breakpoints
const theme = createTheme({
  typography: {
    fontFamily: '"Poppins", "Roboto", "Helvetica", "Arial", sans-serif',
  },
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 900,
      lg: 1200,
      xl: 1536,
    },
  },
  components: {
    MuiContainer: {
      styleOverrides: {
        root: {
          paddingLeft: '16px',
          paddingRight: '16px',
          '@media (max-width: 600px)': {
            paddingLeft: '8px',
            paddingRight: '8px',
          },
        },
      },
    },
  },
});

function App() {
  const [currentSection, setCurrentSection] = useState(0);
  const muiTheme = useTheme();
  const isMobile = useMediaQuery(muiTheme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(muiTheme.breakpoints.down('md'));
  const [formData, setFormData] = useState<FormData>({
    // General Information
    companyName: '',

    // Energy Consumption
    energyConsumptionType: '',
    renewableEnergyElements: [],
    nonRenewableEnergyElements: [],
    windElectricity: '',
    solarElectricity: '',
    otherRenewableElectricity: '',
    gridElectricityConsumed: '',
    fuelConsumed: '',
    otherEnergyConsumed: '',
    totalEnergyConsumed: '',
    gridEnergyPercentage: '',
    renewablePercentage: '',
    numberOfEmployees: '',
    energyPerEmployee: '',

    // Water Consumption
    waterSource: [],
    totalWaterWithdrawn: '',
    totalWaterConsumed: '',
    waterReused: '',
    highWaterStressRegions: '',
    waterWithdrawnStress: '',
    waterConsumedStress: '',

    // Emissions
    emissionSources: [],
    dieselUnits: '',
    dieselCO2: '',
    petrolUnits: '',
    petrolCO2: '',
    keroseneUnits: '',
    keroseneCO2: '',
    electricityUnits: '',
    electricityCO2: '',

    // Waste Management
    wasteActions: [],
    ewasteQuantity: '',
    ewasteHazardous: '',
    paperWasteQuantity: '',
    paperWasteHazardous: '',
    foodWasteQuantity: '',
    foodWasteHazardous: '',
    paperRecycledQuantity: '',
    paperRecycledPercentage: '',
    plasticRecycledQuantity: '',
    plasticRecycledPercentage: '',
    organicWasteQuantity: '',
    landfillWaste: '',
    landfillQuantity: '',

    // GHG Emissions
    naturalGas: '',
    distillateFuelOil: '',
    gasoline: '',
    refrigerants: '',
    purchasedElectricity: '',
    heatingCooling: '',
    travel: '',
    purchasedGoods: '',
    upstreamTransportation: '',
    wasteFromOperations: '',

    // Compliance
    ehsPractices: '',
    nonCompliance: '',
    finesPenalties: '',
    nonComplianceDetails: '',
    codeOfConduct: '',
    environmentalRegulations: '',

    // Assessments
    supplierRiskAssessment: '',
    supplierAssessmentFrequency: '',
    supplierAudits: '',
    correctiveActionPlans: '',
    carbonReductionTargets: '',

    // Financial Contributions
    financialContributions: '',
    directFinancialValue: '',
    indirectFinancialValue: '',
    inKindContributions: '',
    directInKindValue: '',
    indirectInKindValue: '',
    inKindEstimation: '',
    sbtiTargets: '',
    sbtiDescription: ''
  });

  const [submitted, setSubmitted] = useState(false);
  const [clearDialogOpen, setClearDialogOpen] = useState(false);

  const handleInputChange = (field: keyof FormData) => (event: any) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCheckboxChange = (field: keyof FormData) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.checked
    }));
  };

  const handleMultiSelectChange = (field: keyof FormData, option: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => {
      const currentArray = prev[field] as string[];
      return {
        ...prev,
        [field]: event.target.checked
          ? [...currentArray, option]
          : currentArray.filter(item => item !== option)
      };
    });
  };

  const handleNext = () => {
    if (currentSection < sections.length - 1) {
      setCurrentSection(currentSection + 1);
    }
  };

  const handlePrevious = () => {
    if (currentSection > 0) {
      setCurrentSection(currentSection - 1);
    }
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    console.log('Form submitted:', formData);
    setSubmitted(true);
  };

  const handleClearFormClick = () => {
    setClearDialogOpen(true);
  };

  const handleClearDialogClose = () => {
    setClearDialogOpen(false);
  };

  const handleConfirmClearForm = () => {
    setCurrentSection(0);
    setFormData({
      // General Information
      companyName: '',

      // Energy Consumption
      energyConsumptionType: '',
      renewableEnergyElements: [],
      nonRenewableEnergyElements: [],
      windElectricity: '',
      solarElectricity: '',
      otherRenewableElectricity: '',
      gridElectricityConsumed: '',
      fuelConsumed: '',
      otherEnergyConsumed: '',
      totalEnergyConsumed: '',
      gridEnergyPercentage: '',
      renewablePercentage: '',
      numberOfEmployees: '',
      energyPerEmployee: '',

      // Water Consumption
      waterSource: [],
      totalWaterWithdrawn: '',
      totalWaterConsumed: '',
      waterReused: '',
      highWaterStressRegions: '',
      waterWithdrawnStress: '',
      waterConsumedStress: '',

      // Emissions
      emissionSources: [],
      dieselUnits: '',
      dieselCO2: '',
      petrolUnits: '',
      petrolCO2: '',
      keroseneUnits: '',
      keroseneCO2: '',
      electricityUnits: '',
      electricityCO2: '',

      // Waste Management
      wasteActions: [],
      ewasteQuantity: '',
      ewasteHazardous: '',
      paperWasteQuantity: '',
      paperWasteHazardous: '',
      foodWasteQuantity: '',
      foodWasteHazardous: '',
      paperRecycledQuantity: '',
      paperRecycledPercentage: '',
      plasticRecycledQuantity: '',
      plasticRecycledPercentage: '',
      organicWasteQuantity: '',
      landfillWaste: '',
      landfillQuantity: '',

      // GHG Emissions
      naturalGas: '',
      distillateFuelOil: '',
      gasoline: '',
      refrigerants: '',
      purchasedElectricity: '',
      heatingCooling: '',
      travel: '',
      purchasedGoods: '',
      upstreamTransportation: '',
      wasteFromOperations: '',

      // Compliance
      ehsPractices: '',
      nonCompliance: '',
      finesPenalties: '',
      nonComplianceDetails: '',
      codeOfConduct: '',
      environmentalRegulations: '',

      // Assessments
      supplierRiskAssessment: '',
      supplierAssessmentFrequency: '',
      supplierAudits: '',
      correctiveActionPlans: '',
      carbonReductionTargets: '',

      // Financial Contributions
      financialContributions: '',
      directFinancialValue: '',
      indirectFinancialValue: '',
      inKindContributions: '',
      directInKindValue: '',
      indirectInKindValue: '',
      inKindEstimation: '',
      sbtiTargets: '',
      sbtiDescription: ''
    });
    setSubmitted(false);
    setClearDialogOpen(false);
  };

  // Keep the original resetForm function for the "Submit Another Response" button
  const resetForm = () => {
    setCurrentSection(0);
    setFormData({
      // General Information
      companyName: '',

      // Energy Consumption
      energyConsumptionType: '',
      renewableEnergyElements: [],
      nonRenewableEnergyElements: [],
      windElectricity: '',
      solarElectricity: '',
      otherRenewableElectricity: '',
      gridElectricityConsumed: '',
      fuelConsumed: '',
      otherEnergyConsumed: '',
      totalEnergyConsumed: '',
      gridEnergyPercentage: '',
      renewablePercentage: '',
      numberOfEmployees: '',
      energyPerEmployee: '',

      // Water Consumption
      waterSource: [],
      totalWaterWithdrawn: '',
      totalWaterConsumed: '',
      waterReused: '',
      highWaterStressRegions: '',
      waterWithdrawnStress: '',
      waterConsumedStress: '',

      // Emissions
      emissionSources: [],
      dieselUnits: '',
      dieselCO2: '',
      petrolUnits: '',
      petrolCO2: '',
      keroseneUnits: '',
      keroseneCO2: '',
      electricityUnits: '',
      electricityCO2: '',

      // Waste Management
      wasteActions: [],
      ewasteQuantity: '',
      ewasteHazardous: '',
      paperWasteQuantity: '',
      paperWasteHazardous: '',
      foodWasteQuantity: '',
      foodWasteHazardous: '',
      paperRecycledQuantity: '',
      paperRecycledPercentage: '',
      plasticRecycledQuantity: '',
      plasticRecycledPercentage: '',
      organicWasteQuantity: '',
      landfillWaste: '',
      landfillQuantity: '',

      // GHG Emissions
      naturalGas: '',
      distillateFuelOil: '',
      gasoline: '',
      refrigerants: '',
      purchasedElectricity: '',
      heatingCooling: '',
      travel: '',
      purchasedGoods: '',
      upstreamTransportation: '',
      wasteFromOperations: '',

      // Compliance
      ehsPractices: '',
      nonCompliance: '',
      finesPenalties: '',
      nonComplianceDetails: '',
      codeOfConduct: '',
      environmentalRegulations: '',

      // Assessments
      supplierRiskAssessment: '',
      supplierAssessmentFrequency: '',
      supplierAudits: '',
      correctiveActionPlans: '',
      carbonReductionTargets: '',

      // Financial Contributions
      financialContributions: '',
      directFinancialValue: '',
      indirectFinancialValue: '',
      inKindContributions: '',
      directInKindValue: '',
      indirectInKindValue: '',
      inKindEstimation: '',
      sbtiTargets: '',
      sbtiDescription: ''
    });
    setSubmitted(false);
  };

    if (submitted) {
    return (
      <Box>
        {/* Header Bar */}
        <AppBar position="static" sx={{ bgcolor: '#A6B28B', mb: 4 }}>
          <Toolbar sx={{
            display: 'flex',
            alignItems: 'center',
            minHeight: { xs: '56px', sm: '64px' },
            px: { xs: 1, sm: 2 }
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mr: { xs: 1, sm: 2 } }}>
              <img
                src={logo}
                alt="SLASSCOM Logo"
                style={{
                  height: isMobile ? '32px' : '40px',
                  width: 'auto',
                  marginRight: isMobile ? '8px' : '16px'
                }}
              />
            </Box>
            <Typography
              variant={isMobile ? "subtitle1" : "h6"}
              component="div"
              sx={{
                flexGrow: 1,
                fontWeight: 'bold',
                textAlign: 'center',
                fontSize: { xs: '0.9rem', sm: '1.25rem' }
              }}
            >
              SLASSCOM - Baseline Matrix
            </Typography>
            {/* Spacer to balance the logo on the left */}
            <Box sx={{ width: { xs: '40px', sm: '56px' } }} />
          </Toolbar>
        </AppBar>

        <Container maxWidth="md" sx={{ py: { xs: 2, sm: 4 }, px: { xs: 1, sm: 2 } }}>
          <Paper elevation={3} sx={{
            p: { xs: 2, sm: 3, md: 4 },
            textAlign: 'center',
            mx: { xs: 1, sm: 0 }
          }}>
            <Alert severity="success" sx={{ mb: 3 }}>
              Thank you for submitting the SLASSCOM Baseline Matrix form!
            </Alert>
            <Typography variant={isMobile ? "h6" : "h5"} gutterBottom>
              Form Submitted Successfully
            </Typography>
            <Typography variant="body1" sx={{ mb: 3, fontSize: { xs: '0.9rem', sm: '1rem' } }}>
              We have received your environmental baseline data and will process it accordingly.
            </Typography>
            <Button
              variant="contained"
              color="success"
              onClick={resetForm}
              sx={{
                minWidth: { xs: '200px', sm: 'auto' },
                fontSize: { xs: '0.9rem', sm: '1rem' }
              }}
            >
              Submit Another Response
            </Button>
          </Paper>
        </Container>
      </Box>
    );
  }

  const renderGeneralInformation = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Company Details
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        For the period January to December 2023
      </Typography> */}

      <TextField
        fullWidth
        label="Name of the Company"
        value={formData.companyName}
        onChange={handleInputChange('companyName')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
    </Box>
  );

  const renderEnergyConsumption = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Efficient consumption of Energy
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Energy consumption to be calculated in KwH. Provide details of energy consumption based on:
      </Typography>

      <FormControl component="fieldset" sx={{ mb: 3 }} required>
        <FormLabel component="legend">Your energy consumption is based on?</FormLabel>
        <RadioGroup
          value={formData.energyConsumptionType}
          onChange={handleInputChange('energyConsumptionType')}
        >
          <FormControlLabel value="renewable" control={<Radio />} label="Renewable Energy" />
          <FormControlLabel value="non-renewable" control={<Radio />} label="Non-renewable energy" />
        </RadioGroup>
      </FormControl>



      <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>
        <FormControl component="fieldset" sx={{ mb: 3 }} required>
          <FormLabel component="legend">Renewable energy is based on which elements?</FormLabel>
          <FormGroup>
            {['Wind', 'Solar', 'Hydropower', 'Biomass', 'Geothermal Energy', 'Ocean Energy', 'Other'].map((element) => (
              <FormControlLabel
                key={element}
                control={
                  <Checkbox
                    checked={formData.renewableEnergyElements.includes(element)}
                    onChange={handleMultiSelectChange('renewableEnergyElements', element)}
                  />
                }
                label={element}
              />
            ))}
          </FormGroup>
        </FormControl>
      </Box>

      <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>
        <FormControl component="fieldset" sx={{ mb: 3 }} required>
          <FormLabel component="legend">Non-renewable energy consumption is based on which elements?</FormLabel>
          <FormGroup>
            {['Coal', 'Oil', 'Natural Gas', 'Nuclear', 'Other'].map((element) => (
              <FormControlLabel
                key={element}
                control={
                  <Checkbox
                    checked={formData.nonRenewableEnergyElements.includes(element)}
                    onChange={handleMultiSelectChange('nonRenewableEnergyElements', element)}
                  />
                }
                label={element}
              />
            ))}
          </FormGroup>
        </FormControl>
      </Box>

      <Box sx={{
        display: 'flex',
        gap: { xs: 1, sm: 2 },
        mb: 2,
        flexWrap: 'wrap',
        flexDirection: { xs: 'column', sm: 'row' }
      }}>
        <TextField
          label="Quantity of Electricity generated from Wind"
          value={formData.windElectricity}
          onChange={handleInputChange('windElectricity')}
          variant="outlined"
          sx={{
            flex: { xs: '1 1 100%', sm: '1 1 300px' },
            minWidth: { xs: '100%', sm: '250px' }
          }}
          required
        />
        <TextField
          label="Quantity of Electricity generated from Solar"
          value={formData.solarElectricity}
          onChange={handleInputChange('solarElectricity')}
          variant="outlined"
          sx={{
            flex: { xs: '1 1 100%', sm: '1 1 300px' },
            minWidth: { xs: '100%', sm: '250px' }
          }}
          required
        />
      </Box>

      <TextField
        fullWidth
        label="Quantity of Electricity generated from other renewable sources"
        value={formData.otherRenewableElectricity}
        onChange={handleInputChange('otherRenewableElectricity')}
        variant="outlined"
        sx={{ mb: 2 }}
        required
      />

      <TextField
        fullWidth
        label="Quantity of Grid Electricity Consumed (KwH)"
        value={formData.gridElectricityConsumed}
        onChange={handleInputChange('gridElectricityConsumed')}
        variant="outlined"
        sx={{ mb: 2 }}
        required
      />

      <TextField
        fullWidth
        label="Quantity of fuel consumed"
        value={formData.fuelConsumed}
        onChange={handleInputChange('fuelConsumed')}
        variant="outlined"
        helperText="Include petrol, diesel, kerosene (and any other fuel) used for vehicles, generators and other machinery"
        sx={{ mb: 2 }}
        required
      />

      <Box sx={{
        display: 'flex',
        gap: { xs: 1, sm: 2 },
        mb: 2,
        flexWrap: 'wrap',
        flexDirection: { xs: 'column', sm: 'row' }
      }}>
        <TextField
          label="Quantity of energy consumed from other sources (KwH)"
          value={formData.otherEnergyConsumed}
          onChange={handleInputChange('otherEnergyConsumed')}
          variant="outlined"
          sx={{
            flex: { xs: '1 1 100%', sm: '1 1 300px' },
            minWidth: { xs: '100%', sm: '250px' }
          }}
          required
        />

        <TextField
          label="Total energy consumed"
          value={formData.totalEnergyConsumed}
          onChange={handleInputChange('totalEnergyConsumed')}
          variant="outlined"
          sx={{
            flex: { xs: '1 1 100%', sm: '1 1 300px' },
            minWidth: { xs: '100%', sm: '250px' }
          }}
          required
        />
      </Box>

      <Box sx={{
        display: 'flex',
        gap: { xs: 1, sm: 2 },
        mb: 2,
        flexWrap: 'wrap',
        flexDirection: { xs: 'column', sm: 'row' }
      }}>
        <FormControl sx={{
          flex: { xs: '1 1 100%', sm: '1 1 300px' },
          minWidth: { xs: '100%', sm: '250px' }
        }} required>
          <InputLabel>Percentage of energy sourced from the grid</InputLabel>
          <Select
            value={formData.gridEnergyPercentage}
            label="Percentage of energy sourced from the grid"
            onChange={handleInputChange('gridEnergyPercentage')}
          >
            <MenuItem value="10-20%">10-20%</MenuItem>
            <MenuItem value="20-40%">20-40%</MenuItem>
            <MenuItem value="40-50%">40-50%</MenuItem>
            <MenuItem value="50-60%">50-60%</MenuItem>
            <MenuItem value="60% and above">60% and above</MenuItem>
          </Select>
        </FormControl>

        <FormControl sx={{
          flex: { xs: '1 1 100%', sm: '1 1 300px' },
          minWidth: { xs: '100%', sm: '250px' }
        }} required>
          <InputLabel>Percentage Renewable energy sourced from the grid</InputLabel>
          <Select
            value={formData.renewablePercentage}
            label="Percentage Renewable energy sourced from the grid"
            onChange={handleInputChange('renewablePercentage')}
          >
            <MenuItem value="10-20%">Less than 20%</MenuItem>
            <MenuItem value="20-40%">20-40%</MenuItem>
            <MenuItem value="40-50%">40-60%</MenuItem>
            <MenuItem value="50-60%">More than 60%</MenuItem>
          </Select>
        </FormControl>
      </Box>


      <Box sx={{
        display: 'flex',
        gap: { xs: 1, sm: 2 },
        mb: 2,
        flexWrap: 'wrap',
        flexDirection: { xs: 'column', sm: 'row' }
      }}>
        <TextField
          label="Number of Employees for the period"
          value={formData.numberOfEmployees}
          onChange={handleInputChange('numberOfEmployees')}
          variant="outlined"
          sx={{
            flex: { xs: '1 1 100%', sm: '1 1 300px' },
            minWidth: { xs: '100%', sm: '250px' }
          }}
          required
        />
        <TextField
          label="Energy Use per employee"
          value={formData.energyPerEmployee}
          onChange={handleInputChange('energyPerEmployee')}
          variant="outlined"
          helperText="Total energy consumed / number of employees"
          sx={{
            flex: { xs: '1 1 100%', sm: '1 1 300px' },
            minWidth: { xs: '100%', sm: '250px' }
          }}
          required
        />
      </Box>
    </Box>
  );

  const renderWaterConsumption = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Efficient consumption of Water
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Water consumption to be calculated in m³. Provide details of water consumption based on
      </Typography>

      <FormControl component="fieldset" sx={{ mb: 3 }} required>
        <FormLabel component="legend">Source of water</FormLabel>
        <FormGroup>
          {['Main supply (Eg: municipal supply)', 'Private Providers', 'Rain water', 'Ground water', 'Surface water'].map((source) => (
            <FormControlLabel
              key={source}
              control={
                <Checkbox
                  checked={formData.waterSource.includes(source)}
                  onChange={handleMultiSelectChange('waterSource', source)}
                />
              }
              label={source}
            />
          ))}
        </FormGroup>
      </FormControl>

      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Water usage for day-to-day operations
      </Typography>

      <Box sx={{
        display: 'flex',
        gap: { xs: 1, sm: 2 },
        mb: 2,
        flexWrap: 'wrap',
        flexDirection: { xs: 'column', sm: 'row' }
      }}>
        <TextField
          label="Total water withdrawn"
          value={formData.totalWaterWithdrawn}
          onChange={handleInputChange('totalWaterWithdrawn')}
          variant="outlined"
          sx={{
            flex: { xs: '1 1 100%', sm: '1 1 300px' },
            minWidth: { xs: '100%', sm: '250px' }
          }}
          required
        />
        <TextField
          label="Total water consumed"
          value={formData.totalWaterConsumed}
          onChange={handleInputChange('totalWaterConsumed')}
          variant="outlined"
          sx={{
            flex: { xs: '1 1 100%', sm: '1 1 300px' },
            minWidth: { xs: '100%', sm: '250px' }
          }}
          required
        />
      </Box>

      <TextField
        fullWidth
        label="Water re-used through treatment facilities"
        value={formData.waterReused}
        onChange={handleInputChange('waterReused')}
        variant="outlined"
        sx={{ mb: 3 }}
        required
      />

      <FormControl component="fieldset" sx={{ mb: 3 }} required>
        <FormLabel component="legend">Do you operate in Regions with High or Extremely High Baseline Water Stress?</FormLabel>
        <RadioGroup
          value={formData.highWaterStressRegions}
          onChange={handleInputChange('highWaterStressRegions')}
        >
          <FormControlLabel value="yes" control={<Radio />} label="Yes" />
          <FormControlLabel value="no" control={<Radio />} label="No" />
        </RadioGroup>
      </FormControl>

      {formData.highWaterStressRegions === 'yes' && (
        <Box>
          <Typography variant="h6" gutterBottom>
            Percentage for each in regions with High or Extremely High Baseline Water Stress
          </Typography>
          <Box sx={{
            display: 'flex',
            gap: { xs: 1, sm: 2 },
            mb: 2,
            flexWrap: 'wrap',
            flexDirection: { xs: 'column', sm: 'row' }
          }}>
            <TextField
              label="Total water withdrawn"
              value={formData.waterWithdrawnStress}
              onChange={handleInputChange('waterWithdrawnStress')}
              variant="outlined"
              sx={{
                flex: { xs: '1 1 100%', sm: '1 1 300px' },
                minWidth: { xs: '100%', sm: '250px' }
              }}
              required
            />
            <TextField
              label="Total water consumed"
              value={formData.waterConsumedStress}
              onChange={handleInputChange('waterConsumedStress')}
              variant="outlined"
              sx={{
                flex: { xs: '1 1 100%', sm: '1 1 300px' },
                minWidth: { xs: '100%', sm: '250px' }
              }}
              required
            />
          </Box>
        </Box>
      )}
    </Box>
  );

  const renderEmissions = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Minimizing discharge of refuse - Emissions
      </Typography>

      <FormControl component="fieldset" sx={{ mb: 3 }} required>
        <FormLabel component="legend">Emissions from</FormLabel>
        <FormGroup>
          {['Fuel', 'Grid power', 'Air travel', 'Road travel', 'Capital goods'].map((source) => (
            <FormControlLabel
              key={source}
              control={
                <Checkbox
                  checked={formData.emissionSources.includes(source)}
                  onChange={handleMultiSelectChange('emissionSources', source)}
                />
              }
              label={source}
            />
          ))}
        </FormGroup>
      </FormControl>

      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Direct and Indirect GHG Emissions
      </Typography>

      {/* Diesel */}
      <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
        Diesel
      </Typography>
      <Box sx={{
        display: 'flex',
        gap: { xs: 1, sm: 2 },
        mb: 2,
        flexWrap: 'wrap',
        flexDirection: { xs: 'column', sm: 'row' }
      }}>
        <TextField
          label="Units consumed (Litres)"
          value={formData.dieselUnits}
          onChange={handleInputChange('dieselUnits')}
          variant="outlined"
          sx={{
            flex: { xs: '1 1 100%', sm: '1 1 300px' },
            minWidth: { xs: '100%', sm: '250px' }
          }}
          required
        />
        <TextField
          label="Total CO2 emission (TCO2)"
          value={formData.dieselCO2}
          onChange={handleInputChange('dieselCO2')}
          variant="outlined"
          sx={{
            flex: { xs: '1 1 100%', sm: '1 1 300px' },
            minWidth: { xs: '100%', sm: '250px' }
          }}
          required
        />
      </Box>

      {/* Petrol */}
      <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
        Petrol
      </Typography>
      <Box sx={{
        display: 'flex',
        gap: { xs: 1, sm: 2 },
        mb: 2,
        flexWrap: 'wrap',
        flexDirection: { xs: 'column', sm: 'row' }
      }}>
        <TextField
          label="Units consumed (Litres)"
          value={formData.petrolUnits}
          onChange={handleInputChange('petrolUnits')}
          variant="outlined"
          sx={{
            flex: { xs: '1 1 100%', sm: '1 1 300px' },
            minWidth: { xs: '100%', sm: '250px' }
          }}
          required
        />
        <TextField
          label="Total CO2 emission (TCO2)"
          value={formData.petrolCO2}
          onChange={handleInputChange('petrolCO2')}
          variant="outlined"
          sx={{
            flex: { xs: '1 1 100%', sm: '1 1 300px' },
            minWidth: { xs: '100%', sm: '250px' }
          }}
          required
        />
      </Box>

      {/* Kerosene */}
      <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
        Kerosene
      </Typography>
      <Box sx={{
        display: 'flex',
        gap: { xs: 1, sm: 2 },
        mb: 2,
        flexWrap: 'wrap',
        flexDirection: { xs: 'column', sm: 'row' }
      }}>
        <TextField
          label="Units consumed (Litres)"
          value={formData.keroseneUnits}
          onChange={handleInputChange('keroseneUnits')}
          variant="outlined"
          sx={{
            flex: { xs: '1 1 100%', sm: '1 1 300px' },
            minWidth: { xs: '100%', sm: '250px' }
          }}
          required
        />
        <TextField
          label="Total CO2 emission (TCO2)"
          value={formData.keroseneCO2}
          onChange={handleInputChange('keroseneCO2')}
          variant="outlined"
          sx={{
            flex: { xs: '1 1 100%', sm: '1 1 300px' },
            minWidth: { xs: '100%', sm: '250px' }
          }}
          required
        />
      </Box>

      {/* Electricity */}
      <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
        Electricity
      </Typography>
      <Box sx={{
        display: 'flex',
        gap: { xs: 1, sm: 2 },
        mb: 2,
        flexWrap: 'wrap',
        flexDirection: { xs: 'column', sm: 'row' }
      }}>
        <TextField
          label="Units consumed (KwH)"
          value={formData.electricityUnits}
          onChange={handleInputChange('electricityUnits')}
          variant="outlined"
          sx={{
            flex: { xs: '1 1 100%', sm: '1 1 300px' },
            minWidth: { xs: '100%', sm: '250px' }
          }}
          required
        />
        <TextField
          label="Total CO2 emission (KG)"
          value={formData.electricityCO2}
          onChange={handleInputChange('electricityCO2')}
          variant="outlined"
          sx={{
            flex: { xs: '1 1 100%', sm: '1 1 300px' },
            minWidth: { xs: '100%', sm: '250px' }
          }}
          required
        />
      </Box>
    </Box>
  );

  const renderGHGEmissions = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Minimizing discharge of refuse to GHG Emissions
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Quantification (Number) of GHG Emissions for reporting (Eg: GHG protocol, quantity, monitoring and
        reporting on GHG gases - CO2, CH4, N20, etc.) for the following;
      </Typography>

      <Box sx={{
        display: 'flex',
        alignItems: 'flex-start',
        justifyContent: 'space-between',
        flexDirection: { xs: 'column', md: 'row' },
        gap: { xs: 2, md: 0 },
        flexWrap: 'wrap'
      }}>
        <FormControl component="fieldset" sx={{
          mb: 3,
          ml: { xs: 0, md: 8 },
          width: { xs: '100%', sm: 'auto' }
        }} required>
          <FormLabel component="legend">Natural gas</FormLabel>
          <RadioGroup
            row={!isMobile}
            value={formData.naturalGas}
            onChange={handleInputChange('naturalGas')}
          >
            <FormControlLabel value="yes" control={<Radio />} label="Yes" />
            <FormControlLabel value="no" control={<Radio />} label="No" />
          </RadioGroup>
        </FormControl>

        <FormControl component="fieldset" sx={{
          mb: 3,
          width: { xs: '100%', sm: 'auto' }
        }} required>
          <FormLabel component="legend">Distillate fuel oil</FormLabel>
          <RadioGroup
            row={!isMobile}
            value={formData.distillateFuelOil}
            onChange={handleInputChange('distillateFuelOil')}
          >
            <FormControlLabel value="yes" control={<Radio />} label="Yes" />
            <FormControlLabel value="no" control={<Radio />} label="No" />
          </RadioGroup>
        </FormControl>

        <FormControl component="fieldset" sx={{
          mb: 3,
          width: { xs: '100%', sm: 'auto' }
        }} required>
          <FormLabel component="legend">Gasoline</FormLabel>
          <RadioGroup
            row={!isMobile}
            value={formData.gasoline}
            onChange={handleInputChange('gasoline')}
          >
            <FormControlLabel value="yes" control={<Radio />} label="Yes" />
            <FormControlLabel value="no" control={<Radio />} label="No" />
          </RadioGroup>
        </FormControl>

        <FormControl component="fieldset" sx={{
          mb: 3,
          mr: { xs: 0, md: 8 },
          width: { xs: '100%', sm: 'auto' }
        }} required>
          <FormLabel component="legend">Refrigerants</FormLabel>
          <RadioGroup
            row={!isMobile}
            value={formData.refrigerants}
            onChange={handleInputChange('refrigerants')}
          >
            <FormControlLabel value="yes" control={<Radio />} label="Yes" />
            <FormControlLabel value="no" control={<Radio />} label="No" />
          </RadioGroup>
        </FormControl>
      </Box>
    </Box>
  );

  const renderWasteManagement = () => (
    <Box>
      <Box sx={{
        display: 'flex',
        gap: { xs: 1, sm: 2 },
        mb: 2,
        flexWrap: 'wrap',
        flexDirection: { xs: 'column', sm: 'row' }
      }}>
        <TextField
          fullWidth
          label="Purchased and used electricity"
          value={formData.purchasedElectricity}
          onChange={handleInputChange('purchasedElectricity')}
          variant="outlined"
          sx={{
            flex: { xs: '1 1 100%', sm: '1 1 300px' },
            minWidth: { xs: '100%', sm: '250px' }
          }}
          required
        />
        <TextField
          fullWidth
          label="Heating and cooling"
          value={formData.heatingCooling}
          onChange={handleInputChange('heatingCooling')}
          variant="outlined"
          sx={{
            flex: { xs: '1 1 100%', sm: '1 1 300px' },
            minWidth: { xs: '100%', sm: '250px' }
          }}
          required
        />
      </Box>

      <Box sx={{
        display: 'flex',
        gap: { xs: 1, sm: 2 },
        mb: 2,
        flexWrap: 'wrap',
        flexDirection: { xs: 'column', sm: 'row' }
      }}>
        <TextField
          fullWidth
          label="Travel (Business & Employee commuting)"
          value={formData.travel}
          onChange={handleInputChange('travel')}
          variant="outlined"
          sx={{
            flex: { xs: '1 1 100%', sm: '1 1 300px' },
            minWidth: { xs: '100%', sm: '250px' }
          }}
          required
        />
        <TextField
          fullWidth
          label="Purchased goods/ services, capital goods"
          value={formData.purchasedGoods}
          onChange={handleInputChange('purchasedGoods')}
          variant="outlined"
          sx={{
            flex: { xs: '1 1 100%', sm: '1 1 300px' },
            minWidth: { xs: '100%', sm: '250px' }
          }}
          required
        />
      </Box>

      <Box sx={{
        display: 'flex',
        gap: { xs: 1, sm: 2 },
        mb: 2,
        flexWrap: 'wrap',
        flexDirection: { xs: 'column', sm: 'row' }
      }}>
        <TextField
          fullWidth
          label="Upstream transportation/ leased assets"
          value={formData.upstreamTransportation}
          onChange={handleInputChange('upstreamTransportation')}
          variant="outlined"
          sx={{
            flex: { xs: '1 1 100%', sm: '1 1 300px' },
            minWidth: { xs: '100%', sm: '250px' }
          }}
          required
        />
        <TextField
          fullWidth
          label="Waste from operations"
          value={formData.wasteFromOperations}
          onChange={handleInputChange('wasteFromOperations')}
          variant="outlined"
          sx={{
            flex: { xs: '1 1 100%', sm: '1 1 300px' },
            minWidth: { xs: '100%', sm: '250px' }
          }}
          required
        />
      </Box>
    </Box>
  );

  const complianceAndImpact = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Compliance and impact on the environment
      </Typography>

      <Box>
        <FormControl component="fieldset" sx={{ mb: 3 }} required>
          <FormLabel component="legend">Are there established Environmental Health and Safety practices established</FormLabel>
          <RadioGroup
            value={formData.ehsPractices}
            onChange={handleInputChange('ehsPractices')}
          >
            <FormControlLabel value="yes" control={<Radio />} label="Yes" />
            <FormControlLabel value="no" control={<Radio />} label="No" />
          </RadioGroup>
        </FormControl>
      </Box>

      <Box>
        <FormControl component="fieldset" sx={{ mb: 3 }} required>
          <FormLabel component="legend">Were there any non compliance with local regulations</FormLabel>
          <RadioGroup
            value={formData.nonCompliance}
            onChange={handleInputChange('nonCompliance')}
          >
            <FormControlLabel value="yes" control={<Radio />} label="Yes" />
            <FormControlLabel value="no" control={<Radio />} label="No" />
          </RadioGroup>
        </FormControl>
      </Box>

      <Box>
        <FormControl component="fieldset" sx={{ mb: 3 }} required>
          <FormLabel component="legend">Were any fines or penalties paid for non-compliance of EHS?</FormLabel>
          <RadioGroup
            value={formData.finesPenalties}
            onChange={handleInputChange('finesPenalties')}
          >
            <FormControlLabel value="yes" control={<Radio />} label="Yes" />
            <FormControlLabel value="no" control={<Radio />} label="No" />
          </RadioGroup>
        </FormControl>
      </Box>

      {formData.finesPenalties === 'yes' && (
        <Box>
          <TextField
            fullWidth
            label="Specify the non-compliance"
            value={formData.nonComplianceDetails}
            onChange={handleInputChange('nonComplianceDetails')}
            variant="outlined"
            sx={{
              flex: { xs: '1 1 100%', sm: '1 1 300px' },
              minWidth: { xs: '100%', sm: '250px' },
              mb: 3
            }}
            required
          />
        </Box>
      )}

      <Box>
        <FormControl component="fieldset" sx={{ mb: 3 }} required>
          <FormLabel component="legend">Is there an approved Client and business code of conduct?</FormLabel>
          <RadioGroup
            value={formData.codeOfConduct}
            onChange={handleInputChange('codeOfConduct')}
          >
            <FormControlLabel value="yes" control={<Radio />} label="Yes" />
            <FormControlLabel value="no" control={<Radio />} label="No" />
          </RadioGroup>
        </FormControl>
      </Box>

      <Box>
        <FormControl component="fieldset" sx={{ mb: 3 }} required>
          <FormLabel component="legend">Does the Company implement Environmental regulations?</FormLabel>
          <RadioGroup
            value={formData.environmentalRegulations}
            onChange={handleInputChange('environmentalRegulations')}
          >
            <FormControlLabel value="yes" control={<Radio />} label="Yes" />
            <FormControlLabel value="no" control={<Radio />} label="No" />
          </RadioGroup>
        </FormControl>
      </Box>

    </Box>
  );

  const environmentalAssessments = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Supplier Environmental Assessments
      </Typography>
      <TextField
        fullWidth
        label="Does the Company conduct detailed risk assessments of suppliers? If Yes, briefly explain the process"
        value={formData.purchasedElectricity}
        onChange={handleInputChange('purchasedElectricity')}
        variant="outlined"
        sx={{ mb: 3 }}
        required
      />

      <TextField
        fullWidth
        label="How frequently are suppliers assessed and reviewed?"
        value={formData.heatingCooling}
        onChange={handleInputChange('heatingCooling')}
        variant="outlined"
        sx={{ mb: 3 }}
        required
      />

      <TextField
        fullWidth
        label="Does the Company conduct supplier audits? (Eg: No. of 3rd party audits done)"
        value={formData.travel}
        onChange={handleInputChange('travel')}
        variant="outlined"
        sx={{ mb: 3 }}
        required
      />

      <TextField
        fullWidth
        label="Are there any corrective action plans in place for high risk suppliers?"
        value={formData.purchasedGoods}
        onChange={handleInputChange('purchasedGoods')}
        variant="outlined"
        sx={{ mb: 3 }}
        required
      />

      <TextField
        fullWidth
        label="Does the Company have any carbon reduction targets? If Yes, briefly explain"
        value={formData.upstreamTransportation}
        onChange={handleInputChange('upstreamTransportation')}
        variant="outlined"
        sx={{ mb: 3 }}
        required
      />
    </Box>
  );

  const environmentCompliance = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Impact on the environment and compliance
      </Typography>

      <FormControl component="fieldset" sx={{ mb: 3 }} required>
        <FormLabel component="legend">Has the Company made any financial contributions, such as, donations, loans, sponsorships, etc. directly or indirectly?</FormLabel>
        <RadioGroup
          value={formData.financialContributions}
          onChange={handleInputChange('financialContributions')}
        >
          <FormControlLabel value="yes" control={<Radio />} label="Yes" />
          <FormControlLabel value="no" control={<Radio />} label="No" />
        </RadioGroup>
      </FormControl>

      {formData.financialContributions === 'yes' && (
        <Box>
          <TextField
            fullWidth
            label="The total monetary value of financial contributions made directly"
            value={formData.directFinancialValue}
            onChange={handleInputChange('directFinancialValue')}
            variant="outlined"
            sx={{ mb: 1 }}
            required
          />
          <TextField
            fullWidth
            label="The total monetary value of financial contributions made indirectly"
            value={formData.indirectFinancialValue}
            onChange={handleInputChange('indirectFinancialValue')}
            variant="outlined"
            sx={{ mb: 3 }}
            required
          />
        </Box>
      )}

      <FormControl component="fieldset" sx={{ mb: 3 }} required>
        <FormLabel component="legend">Has the Company made any in kind contributions, such as, advertising, consultancy, donation of equipment etc. directly or indirectly?</FormLabel>
        <RadioGroup
          value={formData.inKindContributions}
          onChange={handleInputChange('inKindContributions')}
        >
          <FormControlLabel value="yes" control={<Radio />} label="Yes" />
          <FormControlLabel value="no" control={<Radio />} label="No" />
        </RadioGroup>
      </FormControl>

      {formData.inKindContributions === 'yes' && (
        <Box>
          <TextField
            fullWidth
            label="The total monetary value of in-kind contributions made directly"
            value={formData.directInKindValue}
            onChange={handleInputChange('directInKindValue')}
            variant="outlined"
            sx={{ mb: 1 }}
            required
          />
          <TextField
            fullWidth
            label="The total monetary value of in-kind contributions made indirectly"
            value={formData.indirectInKindValue}
            onChange={handleInputChange('indirectInKindValue')}
            variant="outlined"
            sx={{ mb: 1 }}
            required
          />
          <TextField
            fullWidth
            label="How has this monetary value of in-kind contributions been estimated by the Company based on Country and beneficiary?"
            value={formData.inKindEstimation}
            onChange={handleInputChange('inKindEstimation')}
            variant="outlined"
            sx={{ mb: 3 }}
            required
          />
        </Box>
      )}

      <FormControl component="fieldset" sx={{ mb: 3 }} required>
        <FormLabel component="legend">Are targets set based on SBTi</FormLabel>
        <RadioGroup
          value={formData.sbtiTargets}
          onChange={handleInputChange('sbtiTargets')}
        >
          <FormControlLabel value="yes" control={<Radio />} label="Yes" />
          <FormControlLabel value="no" control={<Radio />} label="No" />
        </RadioGroup>
      </FormControl>

      {formData.sbtiTargets === 'yes' && (
        <Box>
          <TextField
            fullWidth
            label="please give a brief description of the targets, timelines and approvals"
            value={formData.sbtiDescription}
            onChange={handleInputChange('sbtiDescription')}
            variant="outlined"
            sx={{
              mb: 1,
              '& .MuiInputBase-input': {
                fontSize: { xs: '0.9rem', sm: '1rem' }
              }
            }}
            required
          />
        </Box>
      )}

    </Box>
  );

  const renderCurrentSection = () => {
    switch (currentSection) {
      case 0:
        return renderGeneralInformation();
      case 1:
        return renderEnergyConsumption();
      case 2:
        return renderWaterConsumption();
      case 3:
        return renderEmissions();
      case 4:
        return renderGHGEmissions();
      case 5:
        return renderWasteManagement();
      case 6:
        return complianceAndImpact();
      case 7:
        return environmentalAssessments();
      case 8:
        return environmentCompliance();
      default:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              {sections[currentSection]}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              This section is under development. Please navigate to other sections.
            </Typography>
          </Box>
        );
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <Box>
        {/* Header Bar */}
        <AppBar position="fixed" sx={{ bgcolor: '#A6B28B', mb: 4 }}>
          <Toolbar sx={{
            display: 'flex',
            alignItems: 'center',
            minHeight: { xs: '56px', sm: '64px' },
            px: { xs: 1, sm: 2 }
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mr: { xs: 1, sm: 2 } }}>
              <img
                src={logo}
                alt="SLASSCOM Logo"
                style={{
                  height: isMobile ? '32px' : '40px',
                  width: 'auto'
                }}
              />
            </Box>
            <Typography
              component="div"
              sx={{
                flexGrow: 1,
                fontWeight: '600',
                textAlign: 'center',
                fontSize: { xs: '1.1rem', sm: '1.5rem', md: '1.75rem' }
              }}
            >
              SLASSCOM - Baseline Matrix
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <img
                src={ebiLogo}
                alt="EBI Logo"
                style={{
                  height: isMobile ? '40px' : isTablet ? '50px' : '60px',
                  width: 'auto'
                }}
              />
            </Box>
          </Toolbar>
        </AppBar>

        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          py: { xs: 2, sm: 4 },
          px: { xs: 1, sm: 2 }
        }}>
          <Container sx={{
            width: '100%',
            maxWidth: { xs: '100%', sm: 'md', md: 'lg' }
          }}>
            <Paper elevation={3} sx={{
              p: { xs: 2, sm: 3, md: 4 },
              bgcolor: '#F9F6F3',
              mt: { xs: 8, sm: 10 },
              mx: { xs: 0.5, sm: 0 }
            }}>
              {/* Progress Indicator */}
              <Box sx={{ mb: { xs: 3, sm: 4 } }}>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  align="center"
                  sx={{
                    mb: 1,
                    fontSize: { xs: '0.8rem', sm: '0.875rem' }
                  }}
                >
                  Section {currentSection + 1} of {sections.length}: {sections[currentSection]}
                </Typography>
                <Box sx={{
                  width: '100%',
                  bgcolor: 'grey.300',
                  borderRadius: 1,
                  height: { xs: 6, sm: 8 }
                }}>
                  <Box
                    sx={{
                      width: `${((currentSection + 1) / sections.length) * 100}%`,
                      bgcolor: '#A6B28B',
                      height: { xs: 6, sm: 8 },
                      borderRadius: 1,
                      transition: 'width 0.3s ease'
                    }}
                  />
                </Box>
              </Box>

              <Divider sx={{ my: 3 }} />

              <Box component="form" onSubmit={handleSubmit} noValidate>
                {renderCurrentSection()}
              </Box>

              {/* Navigation Buttons */}
              <Box sx={{
                display: 'flex',
                justifyContent: 'space-between',
                mt: { xs: 3, sm: 4 },
                flexDirection: { xs: 'column', sm: 'row' },
                gap: { xs: 2, sm: 0 }
              }}>
                <Button
                  variant="outlined"
                  onClick={handlePrevious}
                  disabled={currentSection === 0}
                  color='success'
                  sx={{
                    order: { xs: 2, sm: 1 },
                    minWidth: { xs: '100%', sm: 'auto' },
                    py: { xs: 1.5, sm: 1 }
                  }}
                >
                  <ArrowBack sx={{ mr: { xs: 1, sm: 0 } }} />
                  {isMobile && 'Previous'}
                </Button>

                <Box sx={{
                  display: 'flex',
                  gap: { xs: 1, sm: 2 },
                  order: { xs: 1, sm: 2 },
                  flexDirection: { xs: 'column', sm: 'row' }
                }}>
                  {currentSection === sections.length - 1 ? (
                    <Button
                      variant="contained"
                      onClick={handleSubmit}
                      sx={{
                        minWidth: { xs: '100%', sm: 120 },
                        py: { xs: 1.5, sm: 1 },
                        fontSize: { xs: '0.9rem', sm: '0.875rem' }
                      }}
                      color='success'
                    >
                      Submit Form
                    </Button>
                  ) : (
                    <Button
                      variant="contained"
                      onClick={handleNext}
                      color='success'
                      sx={{
                        minWidth: { xs: '100%', sm: 'auto' },
                        py: { xs: 1.5, sm: 1 }
                      }}
                    >
                      <ArrowForward sx={{ mr: { xs: 1, sm: 0 } }} />
                      {isMobile && 'Next'}
                    </Button>
                  )}

                  <Button
                    variant="outlined"
                    onClick={handleClearFormClick}
                    sx={{
                      minWidth: { xs: '100%', sm: 120 },
                      py: { xs: 1.5, sm: 1 },
                      fontSize: { xs: '0.9rem', sm: '0.875rem' }
                    }}
                    color='success'
                  >
                    Clear Form
                  </Button>
                </Box>
              </Box>
            </Paper>
          </Container>
        </Box>

        {/* Clear Form Confirmation Dialog */}
        <Dialog
          open={clearDialogOpen}
          onClose={handleClearDialogClose}
          aria-labelledby="clear-form-dialog-title"
          aria-describedby="clear-form-dialog-description"
          maxWidth="sm"
          fullWidth
          sx={{
            '& .MuiDialog-paper': {
              mx: { xs: 2, sm: 3 },
              width: { xs: 'calc(100% - 32px)', sm: 'auto' }
            }
          }}
        >
          <DialogTitle
            id="clear-form-dialog-title"
            sx={{
              fontWeight: '600',
              fontSize: { xs: 20, sm: 24 },
              textAlign: 'center',
              px: { xs: 2, sm: 3 },
              py: { xs: 2, sm: 2.5 }
            }}
          >
            Clear form?
          </DialogTitle>
          <DialogContent sx={{ px: { xs: 2, sm: 3 } }}>
            <DialogContentText
              id="clear-form-dialog-description"
              sx={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}
            >
              This will remove your answers from all questions, and cannot be undone.
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{
            p: { xs: 2, sm: 3 },
            pt: 1,
            flexDirection: { xs: 'column', sm: 'row' },
            gap: { xs: 1, sm: 0 }
          }}>
            <Button
              onClick={handleClearDialogClose}
              variant="text"
              sx={{
                color: 'gray',
                fontWeight: '600',
                width: { xs: '100%', sm: 'auto' },
                order: { xs: 2, sm: 1 }
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleConfirmClearForm}
              variant="text"
              sx={{
                color: 'gray',
                fontWeight: '600',
                width: { xs: '100%', sm: 'auto' },
                order: { xs: 1, sm: 2 }
              }}
              autoFocus
            >
              Clear form
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </ThemeProvider>
  );
}

export default App;
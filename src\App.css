/* Responsive styles for the SLASSCOM form */

/* Base styles */
* {
  box-sizing: border-box;
}

/* Mobile-first responsive design */
@media (max-width: 599px) {
  /* Mobile styles */
  .MuiContainer-root {
    padding-left: 8px !important;
    padding-right: 8px !important;
  }

  .MuiPaper-root {
    margin: 4px !important;
  }

  .MuiTextField-root {
    margin-bottom: 12px !important;
  }

  .MuiFormControl-root {
    margin-bottom: 16px !important;
  }

  .MuiTypography-h6 {
    font-size: 1.1rem !important;
  }

  .MuiButton-root {
    font-size: 0.875rem !important;
    padding: 10px 16px !important;
  }
}

@media (min-width: 600px) and (max-width: 899px) {
  /* Tablet styles */
  .MuiContainer-root {
    padding-left: 16px !important;
    padding-right: 16px !important;
  }
}

@media (min-width: 900px) {
  /* Desktop styles */
  .MuiContainer-root {
    padding-left: 24px !important;
    padding-right: 24px !important;
  }
}

/* Ensure form elements are touch-friendly on mobile */
@media (max-width: 599px) {
  .MuiInputBase-input {
    font-size: 16px !important; /* Prevents zoom on iOS */
  }

  .MuiSelect-select {
    font-size: 16px !important;
  }

  .MuiButton-root {
    min-height: 44px !important; /* Touch target size */
  }

  .MuiRadio-root {
    padding: 12px !important;
  }

  .MuiCheckbox-root {
    padding: 12px !important;
  }
}

/* Improve readability on small screens */
@media (max-width: 599px) {
  .MuiFormLabel-root {
    font-size: 0.9rem !important;
  }

  .MuiFormControlLabel-label {
    font-size: 0.9rem !important;
  }

  .MuiTypography-body2 {
    font-size: 0.8rem !important;
  }
}

/* Ensure proper spacing for form groups */
.MuiFormGroup-root {
  gap: 8px;
}

@media (max-width: 599px) {
  .MuiFormGroup-root {
    gap: 4px;
  }
}

/* Responsive dialog styles */
@media (max-width: 599px) {
  .MuiDialog-paper {
    margin: 16px !important;
    width: calc(100% - 32px) !important;
    max-height: calc(100% - 32px) !important;
  }
}